"use client";

import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@workspace/ui/lib/utils";

const labelSectionSpanVariants = cva(
  "relative inline-block bg-foreground -skew-x-22 rounded-xs",
  {
    variants: {
      size: {
        default: "mb-2",
        small: "scale-90",
      },
    },
    defaultVariants: {
      size: "default",
    },
  },
);

const labelSectionTextVariants = cva(
  "inline-block skew-x-22 px-4 text-background text-shadow-none",
  {
    variants: {
      size: {
        default: "text-sm",
        small: "uppercase text-xs -translate-y-px",
      },
    },
    defaultVariants: {
      size: "default",
    },
  },
);

interface LabelSectionProps
  extends React.HTMLAttributes<HTMLSpanElement>,
    VariantProps<typeof labelSectionSpanVariants> {
  textClasses?: string;
  capped?: boolean;
  children: React.ReactNode;
}

function LabelSection({
  className,
  size,
  children,
  textClasses,
  capped,
  ...props
}: LabelSectionProps) {
  return (
    <span
      className={cn(
        labelSectionSpanVariants({ size }),
        className,
        capped &&
          "before:absolute before:top-0 before:-left-1.5 before before:h-full before:block before:w-4 before:bg-foreground before:skew-x-22",
      )}
      {...props}
    >
      <span
        className={cn(
          labelSectionTextVariants({ size }),
          textClasses,
          capped && "pl-2.5",
        )}
      >
        {children}
      </span>
    </span>
  );
}

export { LabelSection };
