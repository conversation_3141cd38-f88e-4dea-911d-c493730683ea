import { useQuery } from "@tanstack/react-query";

// Types from order dialog
export interface ServiceOption {
  id: string;
  name: string;
  description: string;
  price: number;
  image?: string;
}

export interface ExtraOption {
  id: string;
  name: string;
  price: number;
}

export interface Review {
  id: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  comment: string;
  date: string;
  serviceType: string;
}

export interface Question {
  id: string;
  question: string;
  answer: string;
  userName: string;
  date: string;
  serviceType?: string;
  isNicknameHidden?: boolean;
}

export interface ActivityData {
  image: string;
  summary: string;
  details: Record<string, string>;
}

// Mock data
const mockServiceOptions: ServiceOption[] = [
  {
    id: "rank-boost",
    name: "Rank Boost",
    description: "Mevcut rankınızdan hedef rankınıza kadar boost",
    price: 15,
  },
  {
    id: "placement-matches",
    name: "Placement Matches",
    description: "10 placement maçının tamamı",
    price: 10,
  },
  {
    id: "win-boost",
    name: "<PERSON> Boost",
    description: "Belirtilen sayıda kazanım garantisi",
    price: 8,
  },
];

const mockExtraOptions: ExtraOption[] = [
  { id: "priority", name: "Öncelikli İşlem", price: 2 },
  { id: "stream", name: "Canlı Yayın", price: 2 },
  { id: "chat", name: "Oyuncu ile Sohbet", price: 1 },
  { id: "specific-agent", name: "Belirli Agent", price: 3 },
  { id: "duo-queue", name: "Duo Queue", price: 3 },
];

const mockReviews: Review[] = [
  {
    id: "1",
    userName: "ShadowHunter47",
    userAvatar: "/mocks/user/avatars/16880332327628171.webp",
    rating: 5,
    comment:
      "Mükemmel hizmet! Satıcı gerçekten çok profesyonel, sürekli bilgilendirdi beni. Iron 2'den Silver 3'e çıktım sadece 2 günde. Oyun tarzı çok temiz, hiç şüphe çekmedi. Ayrıca çok sabırlı, sorularımı tek tek cevapladı. Kesinlikle tekrar kullanacağım, arkadaşlarıma da tavsiye ettim bile.",
    date: "15.01.2024",
    serviceType: "Rank Boost",
  },
  {
    id: "2",
    userName: "MysticRose",
    userAvatar: "/mocks/user/avatars/16945979240417192.jpg",
    rating: 4,
    comment:
      "Genel olarak memnunum ama başlangıçta biraz gecikmeler oldu. Satıcı çok kibar ve anlayışlı, sürekli özür diledi gecikmeler için. Bronze 1'den Gold 2'ye çıkardı beni. Oyun stilini çok beğendim, Sage ile nasıl oynandığını öğrendim izleyerek. Sadece biraz daha hızlı olabilirdi.",
    date: "10.01.2024",
    serviceType: "Rank Boost",
  },
  {
    id: "3",
    userName: "PhoenixKing",
    userAvatar: "/mocks/user/avatars/17037968936406465.jpg",
    rating: 5,
    comment:
      "Harika deneyim! Satıcı gerçek bir Valorant uzmanı, taktikleri izlemek çok öğreticiydi. Silver 3'ten Platinum 1'e çıktım. En çok etkilendiğim şey, her maçtan sonra kısa bir analiz yapması ve hangi hatalarımı düzeltmem gerektiğini söylemesiydi. Çok profesyonel yaklaşım.",
    date: "08.01.2024",
    serviceType: "Win Boost",
  },
  {
    id: "4",
    userName: "TurkishWolf",
    userAvatar: "/mocks/user/avatars/17498614176963764.webp",
    rating: 3,
    comment:
      "Ortalama bir deneyim. Satıcı iyi oynuyor ama iletişim biraz eksikti. Gold 1'den Platinum 3'e çıkardı ama beklediğimden uzun sürdü. Yine de sonuçtan memnunum, sadece süreç biraz daha organize olabilirdi. Fiyat performans olarak makul.",
    date: "05.01.2024",
    serviceType: "Placement Matches",
  },
  {
    id: "5",
    userName: "CyberNinja",
    userAvatar: "/mocks/user/avatars/17510597291342641.jpg",
    rating: 5,
    comment:
      "İnanılmaz hızlı ve etkili! Bronze 2'den Gold 1'e sadece 1.5 günde çıktım. Satıcı gerçekten çok yetenekli, özellikle Duelist karakterlerle oynama tarzı mükemmel. Her maçta carry yaptı ve hiç şüphe çekmedi. İletişim de çok iyiydi, sürekli güncellemeler verdi.",
    date: "20.01.2024",
    serviceType: "Rank Boost",
  },
  {
    id: "6",
    userName: "ValorantQueen",
    userAvatar: "/mocks/user/avatars/17524511267138870.webp",
    rating: 4,
    comment:
      "Çok memnun kaldım! Satıcı çok sabırlı ve öğretici. Silver 1'den Platinum 2'ye çıkardı. En güzel yanı, sadece boost yapmakla kalmayıp, oyun anlayışımı da geliştirmeme yardımcı olması. Crosshair placement ve positioning konularında çok şey öğrendim.",
    date: "18.01.2024",
    serviceType: "Win Boost",
  },
  {
    id: "7",
    userName: "EsportsGamer",
    userAvatar: "/mocks/user/avatars/17554631948285659.webp",
    rating: 5,
    comment:
      "Profesyonel seviyede hizmet! Diamond 1'den Immortal 1'e çıkardı beni. Oyun tarzı gerçekten etkileyici, özellikle clutch durumlarında nasıl sakin kaldığını görmek çok öğreticiydi. Kesinlikle tavsiye ederim, para karşılığını fazlasıyla veriyor.",
    date: "22.01.2024",
    serviceType: "Rank Boost",
  },
  {
    id: "8",
    userName: "TacticalMaster",
    userAvatar: "/mocks/user/avatars/17573950572482972.jpg",
    rating: 4,
    comment: "Güzel bir deneyimdi.",
    date: "25.01.2024",
    serviceType: "Placement Matches",
  },
];

const mockQuestions: Question[] = [
  {
    id: "1",
    question: "Diamond 2'den Immortal'a kaç maç sürer?",
    answer:
      "Diamond 2'den Immortal'a genellikle 15-25 maç arası sürer. Mevcut MMR'ınıza ve kazanma oranına bağlı olarak değişir. Eğer win streak yakalarsak daha hızlı olabilir.",
    userName: "Mert",
    date: "12.01.2024",
    serviceType: "Rank Boost",
  },
  {
    id: "2",
    question: "Jett main olarak boost alırken hangi agentları oynuyorsunuz?",
    answer:
      "Genellikle Jett, Reyna ve Raze oynuyorum. Eğer takım kompozisyonu gerektirirse Omen veya Sage de oynayabilirim. Hangi agentı tercih ettiğinizi belirtirseniz ona göre oynayabilirim.",
    userName: "Can",
    date: "08.01.2024",
    serviceType: "Rank Boost",
  },
  {
    id: "3",
    question: "EU West serverında ping problemi yaşar mıyız?",
    answer:
      "Hayır, ben de EU West'te oynuyorum ve 20-30 ping ile oynuyorum. Türkiye'den bağlandığım için hiç ping sorunu yaşamıyorum. Rahat bir şekilde oynayabiliriz.",
    userName: "Elif",
    date: "06.01.2024",
    // No serviceType - this is a general question
  },
  {
    id: "4",
    question: "Canlı yayın seçeneğinde Discord'dan mı izleyeceğim?",
    answer:
      "Evet, Discord üzerinden ekran paylaşımı yapıyorum. İsterseniz ses açık da oynayabiliriz, böylece taktikleri de öğrenebilirsiniz. Twitch'te de yayın açabilirim eğer isterseniz.",
    userName: "Burak",
    date: "03.01.2024",
    // No serviceType - this is a general question about streaming
  },
  {
    id: "5",
    question: "Placement maçlarında hangi pozisyonları oynuyorsunuz?",
    answer:
      "Placement maçlarında genellikle Controller veya Sentinel oynuyorum çünkü takım oyunu daha önemli. Omen, Brimstone, Sage ve Cypher ile çok rahatım. Bu şekilde takımın ihtiyacına göre oynayabilirim.",
    userName: "Ahmet",
    date: "15.01.2024",
    serviceType: "Placement Matches",
  },
  {
    id: "6",
    question: "Win boost sırasında kaç maç garantisi veriyorsunuz?",
    answer:
      "Win boost için genellikle %80-85 kazanma oranı garantisi veriyorum. Eğer bu oranın altında kalırsak, eksik maçları ücretsiz tamamlarım. Ortalama 10-15 maçta istediğiniz win sayısına ulaşırız.",
    userName: "Gizli Kullanıcı",
    date: "18.01.2024",
    serviceType: "Win Boost",
    isNicknameHidden: true,
  },
  {
    id: "7",
    question: "Oyun sırasında voice chat kullanıyor musunuz?",
    answer:
      "Evet, eğer isterseniz voice chat kullanabiliriz. Bu şekilde daha iyi koordinasyon sağlarız ve siz de oyun taktiklerini öğrenebilirsiniz. Tabii ki tercihinize bağlı, sessiz de oynayabiliriz.",
    userName: "Zeynep",
    date: "20.01.2024",
    // No serviceType - general question
  },
  {
    id: "8",
    question: "Hesabımın güvenliği konusunda endişelerim var, ne yapmalıyım?",
    answer:
      "Hesap güvenliği konusunda çok dikkatli davranıyorum. Sadece ekran paylaşımı yapıyorum, hiçbir zaman şifrenizi sormam. İsterseniz 2FA açık tutabilirsiniz. Ayrıca boost sonrası şifrenizi değiştirmenizi öneririm.",
    userName: "Gizli Kullanıcı",
    date: "22.01.2024",
    isNicknameHidden: true,
    // No serviceType - general security question
  },
];

const mockActivityData: ActivityData = {
  image: "/dialogs/order/16995403695576185.webp",
  summary:
    "Aslında oynamaya devam ettiğim ve geliştiğim ilk FPS oyunum!! İlk sezonda Iron 3 olarak başladım, şu an Plat 3'üm (✿◠‿◠) FADE takıntılı bir oyuncuyum!! Seninle oynamayı, öğretmeyi/senden öğrenmeyi ve oyunlarımızı kazanırken güzel bir sohbet etmeyi çok isterim 💕 EUW/NA sunucularında oynuyorum. Bu hizmet ile birlikte rank yükseltme, aim geliştirme ve takım oyunu stratejilerini öğrenebilirsiniz.",
  details: {
    RANK: "Diamond 2",
    SUNUCU: "EU West",
    KARAKTER: "Fade",
    "KULLANICI ADI": "nightstar#4567",
    PLATFORM: "PC",
    STİL: "Agresif",
    KİŞİLİK: "Try Hard",
  },
};

// API simulation functions
const fetchServiceOptions = async (): Promise<ServiceOption[]> => {
  await new Promise((resolve) => setTimeout(resolve, 2000));
  return mockServiceOptions;
};

const fetchExtraOptions = async (): Promise<ExtraOption[]> => {
  await new Promise((resolve) => setTimeout(resolve, 2000));
  return mockExtraOptions;
};

const fetchReviews = async (): Promise<Review[]> => {
  await new Promise((resolve) => setTimeout(resolve, 2000));
  return mockReviews;
};

const fetchQuestions = async (): Promise<Question[]> => {
  await new Promise((resolve) => setTimeout(resolve, 2000));
  return mockQuestions;
};

const fetchActivityData = async (): Promise<ActivityData> => {
  await new Promise((resolve) => setTimeout(resolve, 2000));
  return mockActivityData;
};

// React Query hooks - separated by tab

// Order Tab hooks
export const useServiceOptions = () => {
  return useQuery({
    queryKey: ["order", "serviceOptions"],
    queryFn: fetchServiceOptions,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

export const useExtraOptions = () => {
  return useQuery({
    queryKey: ["order", "extraOptions"],
    queryFn: fetchExtraOptions,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

export const useActivityData = () => {
  return useQuery({
    queryKey: ["order", "activityData"],
    queryFn: fetchActivityData,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

// Reviews Tab hooks
export const useReviews = () => {
  return useQuery({
    queryKey: ["reviews", "list"],
    queryFn: fetchReviews,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

// Questions Tab hooks
export const useQuestions = () => {
  return useQuery({
    queryKey: ["questions", "list"],
    queryFn: fetchQuestions,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};
