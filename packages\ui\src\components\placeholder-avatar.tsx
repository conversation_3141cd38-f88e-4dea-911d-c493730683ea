"use client";

import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@workspace/ui/lib/utils";
import { PlaceholderUser } from "@workspace/ui/components/icons/placeholder-user";

const placeholderAvatarVariants = cva(
  "flex items-center justify-center flex-shrink-0 relative overflow-hidden",
  {
    variants: {
      variant: {
        kyc: "w-26 h-36 grid-background-avatar rounded-md border-4 border-input bg-right",
        review: "size-33 rounded-sm bg-muted",
      },
    },
    defaultVariants: {
      variant: "kyc",
    },
  },
);

const placeholderIconVariants = cva("fill-input stroke-muted", {
  variants: {
    variant: {
      kyc: "p-1 h-full w-full stroke-[20px]",
      review: "size-8",
    },
  },
  defaultVariants: {
    variant: "kyc",
  },
});

interface PlaceholderAvatarProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof placeholderAvatarVariants> {}

function PlaceholderAvatar({
  className,
  variant,
  ...props
}: PlaceholderAvatarProps) {
  return (
    <div
      data-slot="placeholder-avatar"
      className={cn(placeholderAvatarVariants({ variant, className }))}
      {...props}
    >
      <PlaceholderUser
        className={cn(placeholderIconVariants({ variant }))}
      />
    </div>
  );
}

export { PlaceholderAvatar, placeholderAvatarVariants };
