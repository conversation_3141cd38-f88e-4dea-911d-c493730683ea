"use client";

import Image from "next/image";
import { z } from "zod";
import { useForm, revalidateLogic } from "@tanstack/react-form";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ist,
  <PERSON>bsTrigger,
  TabsContent,
} from "@workspace/ui/components/tabs";
import {
  StepperVertical,
  Step,
  StepContentActive,
  StepContentInactive,
  StepActivationButton,
  type StepConfig,
} from "@workspace/ui/components/stepper-vertical";
import { StarRating } from "@workspace/ui/components/star-rating";
import { Search } from "@workspace/ui/components/search";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { Label } from "@workspace/ui/components/label";

import { Separator } from "@workspace/ui/components/separator";
import { Badge } from "@workspace/ui/components/badge";
import { PlaceholderAvatar } from "@workspace/ui/components/placeholder-avatar";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { LabelSection } from "@workspace/ui/components/label-section";
import { SeparatorWithPagination } from "@workspace/ui/components/separator-with-pagination";
import {
  Filter,
  Plus,
  ArrowDownNarrowWide,
  UserStar,
  Pencil,
  ConciergeBell,
} from "lucide-react";
import { QuantityPicker } from "@workspace/ui/components/quantity-picker";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from "@workspace/ui/components/dropdown-menu";
import {
  useServiceOptions,
  useExtraOptions,
  useReviews,
  useQuestions,
  useActivityData,
  type ServiceOption,
  type ExtraOption,
  type Review,
  type Question,
  type ActivityData,
} from "@/hooks/queries/order";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationPrevious,
  PaginationNext,
  PaginationEllipsis,
} from "@workspace/ui/components/pagination";
import { cn } from "@workspace/ui/lib/utils";
import { createContext, useContext, useState } from "react";

const orderFormSchema = z.object({
  // Step 1: Service Selection
  selectedService: z
    .string()
    .min(1, "Bir servis seçmelisiniz")
    .nullable()
    .refine((val) => val !== null, "Bir servis seçmelisiniz"),

  // Step 2: Extras Selection (optional)
  selectedExtras: z.array(z.string()).default([]),

  // Step 3: Quantity and Details
  quantity: z
    .number()
    .min(1, "Miktar en az 1 olmalıdır")
    .max(20, "Miktar en fazla 20 olabilir")
    .int("Miktar tam sayı olmalıdır"),
});

export type OrderFormData = z.infer<typeof orderFormSchema>;

// Activity details interface
export interface ActivityDetails {
  rank: string;
  server: string;
  character: string;
  username: string;
  nickname: string;
  platform?: string;
  style?: string;
  personality?: string;
}

// Context for Order Dialog
interface OrderDialogContext {
  // Service data
  serviceImage: string;
  serviceTitle: string;
  serviceDescription: string;
  overallRating: number;

  // Activity details
  activityDetails: ActivityDetails;

  // Customization
  quantityLabel: string;
  durationPerUnit: number;
  durationUnit: string;

  // Order state
  activeTab: string;
  setActiveTab: (tab: string) => void;
  currentStep: number;
  setCurrentStep: (step: number) => void;
  selectedService: string | null;
  setSelectedService: (serviceId: string | null) => void;
  selectedExtras: string[];
  setSelectedExtras: React.Dispatch<React.SetStateAction<string[]>>;
  quantity: number;
  setQuantity: (quantity: number) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  reviewsPage: number;
  setReviewsPage: (page: number) => void;
  questionsPage: number;
  setQuestionsPage: (page: number) => void;

  // Review filters and sorting
  selectedServiceTypes: string[];
  setSelectedServiceTypes: React.Dispatch<React.SetStateAction<string[]>>;
  selectedStarRatings: number[];
  setSelectedStarRatings: React.Dispatch<React.SetStateAction<number[]>>;
  reviewSortBy: string;
  setReviewSortBy: (sortBy: string) => void;

  // Question filters
  selectedQuestionServiceTypes: string[];
  setSelectedQuestionServiceTypes: React.Dispatch<
    React.SetStateAction<string[]>
  >;

  // Data and loading states
  serviceOptions: ServiceOption[];
  extraOptions: ExtraOption[];
  reviews: Review[];
  questions: Question[];
  activityData: ActivityData | undefined;
  isLoadingServiceOptions: boolean;
  isLoadingExtraOptions: boolean;
  isLoadingReviews: boolean;
  isLoadingQuestions: boolean;
  isLoadingActivityData: boolean;

  // Functions
  calculateTotalPrice: () => number;
  calculateExtrasCost: () => number;
  handleServiceSelect: (serviceId: string | null) => void;
  handleExtraToggle: (extraId: string) => void;
  handleOrderSubmit: () => void;
}

const OrderDialogContext = createContext<OrderDialogContext | null>(null);

export const useOrderDialog = () => {
  const context = useContext(OrderDialogContext);
  if (!context) {
    throw new Error("useOrderDialog must be used within OrderDialogProvider");
  }
  return context;
};

export interface OrderDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;

  // Service data
  serviceImage?: string;
  serviceTitle?: string;
  serviceDescription?: string;
  overallRating?: number;

  // Activity details
  activityDetails?: ActivityDetails;

  // Customization
  quantityLabel?: string;
  durationPerUnit?: number;
  durationUnit?: string;

  // Event handlers
  onOrderSubmit?: (orderData: any) => void;
}

const getOrderSteps = (
  currentStep: number,
  formState: any,
  completedSteps: Set<number>,
): StepConfig[] => {
  // Helper function to check if a field is completed (valid and dirty)
  const isFieldCompleted = (fieldName: string) => {
    const field = formState.fieldMeta[fieldName];
    if (!field) return false;

    // Field must be dirty (user has interacted) and have no errors
    return field.isDirty && field.errors.length === 0;
  };

  // Check if service field is completed
  const serviceCompleted =
    isFieldCompleted("selectedService") &&
    formState.values.selectedService !== null;

  // Extras step is always considered completed since it's optional
  // But we check if user has interacted with it
  const extrasCompleted = isFieldCompleted("selectedExtras");

  // Details step requires quantity to be valid and dirty
  const detailsCompleted = isFieldCompleted("quantity");

  return [
    {
      id: "service",
      label: "Servis Seç",
      completed: serviceCompleted || completedSteps.has(0),
    },
    {
      id: "extras",
      label: "Ekstra Ekle",
      completed: extrasCompleted || currentStep > 1 || completedSteps.has(1),
    },
    {
      id: "details",
      label: "Miktar ve Teslimat",
      completed: detailsCompleted || completedSteps.has(2),
    },
  ];
};

// Inactive Step Card Component
interface InactiveStepCardProps {
  title: string;
  description: string;
  buttonText: string;
  stepId: string;
  children?: React.ReactNode;
}

function InactiveStepCard({
  title,
  description,
  buttonText,
  stepId,
  children,
}: InactiveStepCardProps) {
  return (
    <div className="space-y-3 p-4 bg-background rounded-lg border-2 border-ring relative overflow-clip ring-2 ring-border mt-1">
      <div className="absolute top-0 h-full left-0 bg-foreground w-3"></div>
      <div className="absolute top-0 h-full left-4 bg-destructive w-1"></div>
      <div className="ml-6 flex items-center justify-between">
        <div className="space-y-1">
          <p className="text-sm font-medium text-foreground">{title}</p>
          <p className="text-xs text-muted-foreground">{description}</p>
        </div>
        <StepActivationButton stepId={stepId}>
          {buttonText}
        </StepActivationButton>
      </div>
      {children}
    </div>
  );
}

function SodaTotalDisplay() {
  const { calculateTotalPrice } = useOrderDialog();
  const total = calculateTotalPrice();
  return (
    <div className={`relative pl-9 ml-6 ${total === 0 ? "opacity-50" : ""}`}>
      <Image
        src="/assets/soda-2-sm.png"
        alt="Soda"
        width={20}
        height={20}
        className="absolute bottom-0.5 left-0"
      />
      <div>
        <p className="text-sm text-muted-foreground">{"Sipariş Tutarı:"}</p>
        <p className="text-2xl font-bold">{total} SODA</p>
      </div>
    </div>
  );
}

// Order Tab Component
function OrderTab() {
  const contextHandlers = useOrderDialog();
  const {
    currentStep,
    setCurrentStep,
    selectedService,
    serviceOptions,
    selectedExtras,
    setSelectedExtras,
    extraOptions,
    quantity,
    quantityLabel,
    durationPerUnit,
    durationUnit,
    calculateExtrasCost,
    handleOrderSubmit,
    isLoadingServiceOptions,
    isLoadingExtraOptions,
  } = contextHandlers;

  // Track completed steps when next button is clicked
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());

  // Initialize TanStack Form
  const form = useForm({
    defaultValues: {
      selectedService: selectedService,
      selectedExtras: selectedExtras,
      quantity: quantity,
    },
    validationLogic: revalidateLogic({
      mode: "blur",
      modeAfterSubmission: "change",
    }),
    onSubmit: async () => {
      handleOrderSubmit();
    },
  });

  // Sync form values with context state
  const handleServiceSelect = (serviceId: string | null) => {
    form.setFieldValue("selectedService", serviceId);
    contextHandlers.handleServiceSelect(serviceId); // Update context state
  };

  const handleExtraToggle = (extraId: string) => {
    const currentExtras = form.getFieldValue("selectedExtras");
    const newExtras = currentExtras.includes(extraId)
      ? currentExtras.filter((id: string) => id !== extraId)
      : [...currentExtras, extraId];
    form.setFieldValue("selectedExtras", newExtras);
    contextHandlers.handleExtraToggle(extraId); // Update context state
  };

  const handleQuantityChange = (newQuantity: number) => {
    form.setFieldValue("quantity", newQuantity);
    contextHandlers.setQuantity(newQuantity); // Update context state
  };

  const handleReset = () => {
    handleServiceSelect(null);

    const newExtras: string[] = [];
    form.setFieldValue("selectedExtras", newExtras);
    setSelectedExtras(newExtras);

    handleQuantityChange(1);

    setCurrentStep(0);
    setCompletedSteps(new Set());
    form.reset();
  };

  return (
    <TabsContent value="siparis" className="mt-0 h-full flex flex-col flex-1">
      <div className="flex-1 scrollbar overflow-y-auto pr-4">
        <StepperVertical
          steps={getOrderSteps(currentStep, form.state, completedSteps)}
          currentStep={currentStep}
          onStepChange={setCurrentStep}
          autoScroll={true}
          className="max-w-xl mx-auto"
        >
          {/* Step 1: Service Selection */}
          <Step stepId="service">
            <StepContentActive>
              <form.Field
                name="selectedService"
                validators={{
                  onChange: ({ value }) => {
                    if (!value) return "Bir servis seçmelisiniz";
                    return undefined;
                  },
                }}
              >
                {(field) => (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Servis Seç</h3>
                    <div className="grid grid-cols-2 gap-4">
                      {isLoadingServiceOptions
                        ? Array.from({ length: 3 }).map((_, index) => (
                            <Skeleton
                              key={`service-skeleton-${index}`}
                              className="h-34 w-full rounded-lg"
                            />
                          ))
                        : serviceOptions.map((service) => (
                            <button
                              key={service.id}
                              type="button"
                              onClick={() => {
                                handleServiceSelect(service.id);
                                field.handleBlur();
                              }}
                              className={cn(
                                "h-34 flex flex-col p-5 border-2 border-border rounded-lg text-left bg-background overflow-clip relative",
                                field.state.value === service.id
                                  ? "ring-primary ring-3"
                                  : "border-border hover:border-border-foreground",
                              )}
                            >
                              {field.state.value === service.id && (
                                <div className="absolute inset-1 overflow-clip rounded-sm after:black after:absolute after:size-12 after:-top-6.5 after:-left-6.5 after:rotate-45 after:bg-primary p-1 "></div>
                              )}
                              <div className="space-y-2.5 relative overflow-clip">
                                <div className="flex gap-2 justify-between">
                                  <h4 className="font-medium text-base leading-4.5 place-self-center">
                                    {service.name}
                                  </h4>
                                  <Badge
                                    variant="default"
                                    className="place-self-start"
                                  >
                                    {service.price} SODA
                                  </Badge>
                                </div>
                                <p className="text-sm text-muted-foreground">
                                  {service.description}
                                </p>
                              </div>
                            </button>
                          ))}
                    </div>
                    {field.state.meta.isTouched &&
                      field.state.meta.errors.length > 0 && (
                        <p className="text-sm text-destructive">
                          {field.state.meta.errors[0]}
                        </p>
                      )}
                  </div>
                )}
              </form.Field>
            </StepContentActive>
            <StepContentInactive>
              <InactiveStepCard
                title="Servis Seçildi"
                description={`${
                  selectedService &&
                  serviceOptions.find((s) => s.id === selectedService)?.name
                } (${
                  selectedService &&
                  serviceOptions.find((s) => s.id === selectedService)?.price
                } SODA)`}
                buttonText="DÜZENLE"
                stepId="service"
              />
            </StepContentInactive>
          </Step>

          {/* Step 2: Extras Selection */}
          <Step stepId="extras">
            <StepContentActive>
              <form.Field name="selectedExtras">
                {(field) => (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">
                      Ekstra Ekle{" "}
                      <span className="text-sm text-muted-foreground ml-0.5">
                        (İsteğe Bağlı)
                      </span>
                    </h3>
                    <div className="grid grid-cols-2 gap-3">
                      {isLoadingExtraOptions
                        ? Array.from({ length: 5 }).map((_, index) => (
                            <Skeleton
                              key={`extra-skeleton-${index}`}
                              className="h-16 w-full rounded-lg"
                            />
                          ))
                        : extraOptions.map((extra) => (
                            <div
                              key={extra.id}
                              className={cn(
                                "flex items-center space-x-3 p-3 border-2 border-border rounded-lg bg-background",
                                field.state.value.includes(extra.id) &&
                                  "ring-2 ring-primary",
                              )}
                            >
                              <Checkbox
                                id={extra.id}
                                checked={field.state.value.includes(extra.id)}
                                onCheckedChange={() => {
                                  handleExtraToggle(extra.id);
                                  field.handleBlur();
                                }}
                                size="large"
                              />
                              <Label
                                htmlFor={extra.id}
                                className="flex-1 cursor-pointer"
                              >
                                <div className="flex-1 flex justify-between items-center">
                                  <span>{extra.name}</span>
                                  <Badge variant="default">
                                    +{extra.price} SODA
                                  </Badge>
                                </div>
                              </Label>
                            </div>
                          ))}
                    </div>
                  </div>
                )}
              </form.Field>
            </StepContentActive>
            <StepContentInactive>
              <InactiveStepCard
                title={
                  selectedExtras.length > 0 ? "Ekstra Eklendi" : "Ekstra Ekle"
                }
                description={
                  selectedExtras.length > 0
                    ? `${selectedExtras.length} seçenek seçildi (${calculateExtrasCost()} SODA)`
                    : "Ekstra seçenek eklenmedi"
                }
                buttonText={selectedExtras.length > 0 ? "DÜZENLE" : "EKLE"}
                stepId="extras"
              >
                {/* {selectedExtras.length > 0 && (
                  <div className="grid grid-cols-2 gap-x-2 gap-y-1 text-xs text-muted-foreground">
                    {selectedExtras.map((extraId) => {
                      const extra = extraOptions.find((e) => e.id === extraId);
                      return extra ? (
                        <p key={extraId}>
                          • {extra.name} (+{extra.price} SODA)
                        </p>
                      ) : null;
                    })}
                  </div>
                )} */}
              </InactiveStepCard>
            </StepContentInactive>
          </Step>

          {/* Step 3: Quantity and Duration */}
          <Step stepId="details">
            <StepContentActive className="mb-5">
              <form.Field
                name="quantity"
                validators={{
                  onChange: ({ value }) => {
                    if (value < 1) return "Miktar en az 1 olmalıdır";
                    if (value > 20) return "Miktar en fazla 20 olabilir";
                    if (!Number.isInteger(value))
                      return "Miktar tam sayı olmalıdır";
                    return undefined;
                  },
                }}
              >
                {(field) => (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">
                      Aktivite Süresi Belirle
                    </h3>
                    <div className="fake-text-stroke-muted">
                      <div className="space-y-4">
                        <p className="text-sm leading-6 text-muted-foreground w-5/6">
                          {`Her bir ${quantityLabel} ${durationPerUnit} ${durationUnit}lik aktivite süresine denk
                          gelmektedir. Örneğin 2 adet seçimi ${durationPerUnit * 2} ${durationUnit}lik
                          aktivite anlamına gelir.`}
                        </p>
                        <div className="flex items-center gap-2">
                          <QuantityPicker
                            value={field.state.value}
                            onChange={(value) => {
                              handleQuantityChange(value);
                              field.handleBlur();
                            }}
                            onBlur={field.handleBlur}
                            min={1}
                            max={20}
                            label={quantityLabel}
                          />
                        </div>
                        <p className="text-sm font-semibold pt-2">
                          Toplam aktivite süresi:{" "}
                          {field.state.value * durationPerUnit} {durationUnit}
                        </p>
                        {field.state.meta.isTouched &&
                          field.state.meta.errors.length > 0 && (
                            <p className="text-sm text-destructive">
                              {field.state.meta.errors[0]}
                            </p>
                          )}
                      </div>
                    </div>
                  </div>
                )}
              </form.Field>
            </StepContentActive>
            <StepContentInactive>
              <InactiveStepCard
                title="Aktivite Süresi Belirlendi"
                description={`${quantity} ${quantityLabel} girildi`}
                buttonText="DÜZENLE"
                stepId="details"
              />
            </StepContentInactive>
          </Step>
        </StepperVertical>
      </div>

      <Separator />

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center pt-4 px-6">
        <SodaTotalDisplay />

        <div className="flex items-center gap-2">
          {currentStep > 0 && (
            <Button
              size="medium"
              disabled={!form.state.isDirty}
              onClick={handleReset}
              className="w-30"
            >
              SIFIRLA
            </Button>
          )}
          {currentStep < 2 ? (
            <Button
              size="medium"
              variant="primary"
              onClick={() => {
                // Mark current step as completed when next button is clicked
                setCompletedSteps((prev) => new Set(prev).add(currentStep));
                setCurrentStep(Math.min(2, currentStep + 1));
              }}
              disabled={currentStep === 0 && !selectedService}
            >
              DEVAM
            </Button>
          ) : (
            <Button
              size="medium"
              variant="primary"
              onClick={handleOrderSubmit}
              disabled={!selectedService}
            >
              SİPARİŞ VER
            </Button>
          )}
        </div>
      </div>
    </TabsContent>
  );
}

// Reviews Tab Component
function ReviewSortDropdown() {
  const { reviewSortBy, setReviewSortBy } = useOrderDialog();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button size="icon" variant="default">
          <ArrowDownNarrowWide className="size-6" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-40" align="end">
        <DropdownMenuLabel>Tarihe Göre</DropdownMenuLabel>
        <DropdownMenuRadioGroup
          value={reviewSortBy}
          onValueChange={setReviewSortBy}
        >
          <DropdownMenuRadioItem value="date-desc">
            Eskiye
          </DropdownMenuRadioItem>
          <DropdownMenuRadioItem value="date-asc">Yeniye</DropdownMenuRadioItem>
        </DropdownMenuRadioGroup>

        <DropdownMenuSeparator />

        <DropdownMenuLabel>Puana Göre</DropdownMenuLabel>
        <DropdownMenuRadioGroup
          value={reviewSortBy}
          onValueChange={setReviewSortBy}
        >
          <DropdownMenuRadioItem value="rating-desc">
            Azalan
          </DropdownMenuRadioItem>
          <DropdownMenuRadioItem value="rating-asc">
            Çoğalan
          </DropdownMenuRadioItem>
        </DropdownMenuRadioGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function ReviewFilterDropdown() {
  const {
    selectedServiceTypes,
    setSelectedServiceTypes,
    selectedStarRatings,
    setSelectedStarRatings,
    serviceOptions,
    reviews,
  } = useOrderDialog();

  // Get unique star ratings that exist in the data
  const availableStarRatings = [...new Set(reviews.map((r) => r.rating))].sort(
    (a, b) => b - a,
  );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          size="icon"
          variant={
            selectedServiceTypes.length > 0 || selectedStarRatings.length > 0
              ? "primary"
              : "default"
          }
        >
          <Filter
            className={cn(
              "size-6",
              (selectedServiceTypes.length > 0 ||
                selectedStarRatings.length > 0) &&
                "fill-background",
            )}
          />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="max-w-xl" align="end">
        {/* Service Type Filter */}
        <DropdownMenuLabel>Hizmet Türü</DropdownMenuLabel>
        {serviceOptions.map((service) => (
          <DropdownMenuCheckboxItem
            key={service.id}
            checked={selectedServiceTypes.includes(service.name)}
            onCheckedChange={(checked) => {
              if (checked) {
                setSelectedServiceTypes([
                  ...selectedServiceTypes,
                  service.name,
                ]);
              } else {
                setSelectedServiceTypes(
                  selectedServiceTypes.filter((t) => t !== service.name),
                );
              }
            }}
          >
            {service.name}
          </DropdownMenuCheckboxItem>
        ))}

        <DropdownMenuSeparator />

        {/* Star Rating Filter */}
        <DropdownMenuLabel>Yıldız Puanı</DropdownMenuLabel>
        {availableStarRatings.map((rating) => (
          <DropdownMenuCheckboxItem
            key={rating}
            checked={selectedStarRatings.includes(rating)}
            onCheckedChange={(checked) => {
              if (checked) {
                setSelectedStarRatings([...selectedStarRatings, rating]);
              } else {
                setSelectedStarRatings(
                  selectedStarRatings.filter((r) => r !== rating),
                );
              }
            }}
          >
            {rating} Yıldız
          </DropdownMenuCheckboxItem>
        ))}

        {(selectedServiceTypes.length > 0 ||
          selectedStarRatings.length > 0) && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => {
                setSelectedServiceTypes([]);
                setSelectedStarRatings([]);
              }}
              className="justify-center"
            >
              Sıfırla
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

interface ReviewsListProps {
  reviews: Review[];
  isLoading: boolean;
  expandedReviews: Set<string>;
  onToggleExpanded: (reviewId: string) => void;
}

function ReviewsList({
  reviews,
  isLoading,
  expandedReviews,
  onToggleExpanded,
}: ReviewsListProps) {
  const charLimit = 115;

  if (isLoading) {
    return Array.from({ length: 5 }).map((_, index) => (
      <div
        key={`review-skeleton-${index}`}
        className="drop-shadow-sm max-w-xl mx-auto"
      >
        <div className="flex gap-4">
          <Skeleton className="size-33 rounded-sm" />
          <div className="relative flex-1 space-y-3">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-16" />
              </div>
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
            <div className="flex justify-between items-center">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-16" />
            </div>
          </div>
        </div>
      </div>
    ));
  }

  return reviews.map((review) => {
    const isExpanded = expandedReviews.has(review.id);
    const needsToggle = review.comment.length > charLimit;

    const toggleExpanded = () => {
      onToggleExpanded(review.id);
    };

    return (
      <div key={review.id} className="drop-shadow-sm max-w-xl mx-auto">
        <div className="flex gap-4">
          {/* Square Avatar - larger size */}
          {review.userAvatar ? (
            <div className="size-33 rounded-sm bg-muted flex items-center justify-center shrink-0 overflow-clip">
              <Image
                src={review.userAvatar}
                alt={review.userName}
                width={500}
                height={500}
                className="object-cover"
              />
            </div>
          ) : (
            <PlaceholderAvatar variant="review" />
          )}

          {/* Message Box - ensure same height as avatar */}
          <div className="relative flex-1">
            <div className="absolute z-10">
              <div className="flex items-center gap-2 justify-between">
                {/* Username in rounded box */}
                <div className="bg-foreground px-4 py-0.5 rounded-full text-left ml-2">
                  <span className="font-medium text-secondary-foreground text-shadow-none text-sm">
                    {review.userName}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex h-full gap-3 pt-3">
              <div className="bg-background p-6 pt-6 pb-5 rounded-sm relative flex-1">
                <StarRating
                  value={review.rating}
                  size="medium"
                  className="absolute -top-3 right-3.5"
                />
                <div className="absolute bottom-0 right-0 rounded-br-sm  overflow-clip">
                  <div className="text-xs text-background bg-secondary text-shadow-none pl-3 pr-4 py-0.5 -skew-x-22 translate-x-1 rounded-tl-xs">
                    <div className="skew-x-22 translate-y-px">
                      {review.date}
                    </div>
                  </div>
                </div>
                <p className="text-sm leading-6 text-muted-foreground tracking-wide">
                  <span className="text-foreground">{review.serviceType}</span>
                  {" - "}
                  <span>
                    {needsToggle && !isExpanded ? (
                      <>
                        {review.comment.substring(0, charLimit)}...
                        <button
                          onClick={toggleExpanded}
                          className="ml-1.5 text-sm text-foreground hover:underline"
                        >
                          Devamı
                        </button>
                      </>
                    ) : needsToggle && isExpanded ? (
                      <>
                        {review.comment}{" "}
                        <button
                          onClick={toggleExpanded}
                          className="text-sm text-foreground hover:underline"
                        >
                          {"Daha az"}
                        </button>
                      </>
                    ) : (
                      <>{review.comment}</>
                    )}
                  </span>
                </p>
              </div>
              {/* Talking Bubble */}
              <div className="absolute top-8 -left-2 scale-y-50">
                <div className="size-5 bg-background rotate-45 "></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  });
}

function ReviewsTab() {
  const {
    searchQuery,
    setSearchQuery,
    reviews,
    reviewsPage,
    setReviewsPage,
    selectedServiceTypes,
    selectedStarRatings,
    reviewSortBy,
    isLoadingReviews,
  } = useOrderDialog();

  const itemsPerPage = 5;
  const [expandedReviews, setExpandedReviews] = useState<Set<string>>(
    new Set(),
  );

  const handleToggleExpanded = (reviewId: string) => {
    const newExpanded = new Set(expandedReviews);
    if (expandedReviews.has(reviewId)) {
      newExpanded.delete(reviewId);
    } else {
      newExpanded.add(reviewId);
    }
    setExpandedReviews(newExpanded);
  };

  const getPageNumbers = (currentPage: number, totalPages: number) => {
    const pages: (number | "ellipsis")[] = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      pages.push(1);
      if (currentPage > 3) {
        pages.push("ellipsis");
      }
      const start = Math.max(2, currentPage - 1);
      const end = Math.min(totalPages - 1, currentPage + 1);
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      if (currentPage < totalPages - 2) {
        pages.push("ellipsis");
      }
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }
    return pages;
  };

  // Filter and sort reviews
  const filteredAndSortedReviews = reviews
    .filter((review) => {
      // Search filter
      if (
        searchQuery &&
        !review.comment.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !review.userName.toLowerCase().includes(searchQuery.toLowerCase())
      ) {
        return false;
      }

      // Service type filter
      if (
        selectedServiceTypes.length > 0 &&
        !selectedServiceTypes.includes(review.serviceType)
      ) {
        return false;
      }

      // Star rating filter
      if (
        selectedStarRatings.length > 0 &&
        !selectedStarRatings.includes(review.rating)
      ) {
        return false;
      }

      return true;
    })
    .sort((a, b) => {
      switch (reviewSortBy) {
        case "date-desc":
          return new Date(b.date).getTime() - new Date(a.date).getTime();
        case "date-asc":
          return new Date(a.date).getTime() - new Date(b.date).getTime();
        case "rating-desc":
          return b.rating - a.rating;
        case "rating-asc":
          return a.rating - b.rating;
        default:
          return 0;
      }
    });

  const paginatedReviews = filteredAndSortedReviews.slice(
    (reviewsPage - 1) * itemsPerPage,
    reviewsPage * itemsPerPage,
  );

  // Calculate statistics
  const totalRatings = reviews.length + 15; // Additional ratings without reviews
  const totalReviews = reviews.filter(
    (r) => r.comment.trim().length > 0,
  ).length;

  return (
    <TabsContent
      value="degerlendirme"
      className="mt-0 h-full flex flex-col space-y-4 flex-1"
      data-tab="degerlendirme"
    >
      {/* Header with Statistics and Controls */}
      <div className="-mt-2 flex items-center justify-between">
        <p className="text-sm text-muted-foreground fake-text-stroke-muted">
          {totalRatings} kişi puanladı, {totalReviews} kişi değerlendirme yazdı.
        </p>

        {/* Controls: Filter, Sort, Search */}
        <div className="flex gap-2">
          <ReviewFilterDropdown />
          <ReviewSortDropdown />
          <Search
            placeholder="Değerlendirmelerde ara..."
            value={searchQuery}
            onValueChange={setSearchQuery}
            className="w-60"
          />
        </div>
      </div>

      {/* Reviews List */}
      <div className="flex-1 pb-15 pt-5 overflow-y-scroll space-y-10 scrollbar text-sm">
        <ReviewsList
          reviews={paginatedReviews}
          isLoading={isLoadingReviews}
          expandedReviews={expandedReviews}
          onToggleExpanded={handleToggleExpanded}
        />
      </div>

      {/* Pagination */}
      <Pagination>
        <PaginationContent className="flex justify-between gap-2 w-full">
          <PaginationItem>
            <PaginationPrevious
              href="#"
              onClick={(e) => {
                e.preventDefault();
                if (reviewsPage > 1) setReviewsPage(reviewsPage - 1);
              }}
              className={
                reviewsPage === 1 ? "pointer-events-none opacity-50" : ""
              }
            />
          </PaginationItem>

          <div className="flex gap-2">
            {getPageNumbers(
              reviewsPage,
              Math.ceil(filteredAndSortedReviews.length / itemsPerPage),
            ).map((page, index) => (
              <PaginationItem key={index}>
                {page === "ellipsis" ? (
                  <PaginationEllipsis />
                ) : (
                  <PaginationLink
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      setReviewsPage(page as number);
                    }}
                    isActive={reviewsPage === page}
                    size="icon"
                  >
                    {page}
                  </PaginationLink>
                )}
              </PaginationItem>
            ))}
          </div>

          <PaginationItem>
            <PaginationNext
              href="#"
              onClick={(e) => {
                e.preventDefault();
                if (
                  reviewsPage <
                  Math.ceil(filteredAndSortedReviews.length / itemsPerPage)
                ) {
                  setReviewsPage(reviewsPage + 1);
                }
              }}
              className={
                reviewsPage ===
                Math.ceil(filteredAndSortedReviews.length / itemsPerPage)
                  ? "pointer-events-none opacity-50"
                  : ""
              }
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </TabsContent>
  );
}

// Questions Tab Component
function QuestionFilterDropdown() {
  const {
    selectedQuestionServiceTypes,
    setSelectedQuestionServiceTypes,
    questions,
  } = useOrderDialog();

  const availableServiceTypes = [
    ...new Set([
      "Genel",
      ...questions
        .map((q) => q.serviceType)
        .filter((type): type is string => Boolean(type)),
    ]),
  ];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          size="icon"
          variant={
            selectedQuestionServiceTypes.length > 0 ? "primary" : "default"
          }
        >
          <Filter
            className={cn(
              "size-6",
              selectedQuestionServiceTypes.length > 0 && "fill-background",
            )}
          />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-60" align="end">
        <DropdownMenuLabel>Hizmet Türü</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {availableServiceTypes.map((serviceType) => (
          <DropdownMenuCheckboxItem
            key={serviceType}
            checked={selectedQuestionServiceTypes.includes(serviceType)}
            onCheckedChange={(checked) => {
              if (checked) {
                setSelectedQuestionServiceTypes([
                  ...selectedQuestionServiceTypes,
                  serviceType,
                ]);
              } else {
                setSelectedQuestionServiceTypes(
                  selectedQuestionServiceTypes.filter((t) => t !== serviceType),
                );
              }
            }}
          >
            {serviceType}
          </DropdownMenuCheckboxItem>
        ))}

        {selectedQuestionServiceTypes.length > 0 && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => {
                setSelectedQuestionServiceTypes([]);
              }}
              className="justify-center"
            >
              Sıfırla
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

interface QuestionsListProps {
  questions: Question[];
  isLoading: boolean;
}

function QuestionsList({ questions, isLoading }: QuestionsListProps) {
  if (isLoading) {
    return Array.from({ length: 3 }).map((_, index) => (
      <div
        key={`question-skeleton-${index}`}
        className="space-y-3 max-w-xl mx-auto"
      >
        <div className="space-y-1.5">
          <Skeleton className="h-6 w-full" />
          <div className="flex gap-3 items-center">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-5 w-24 rounded-full" />
          </div>
        </div>
        <div className="pl-7 p-6 border-primary bg-background rounded-sm drop-shadow-sm">
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </div>
      </div>
    ));
  }

  return questions.map((qa) => (
    <div key={qa.id} className="space-y-3 max-w-xl mx-auto">
      <div className="space-y-1.5">
        <h4 className="flex-1 text-base leading-6">{qa.question}</h4>
        <p className="flex gap-3 items-center text-sm text-muted-foreground">
          {qa.isNicknameHidden ? "Gizli Kullanıcı" : qa.userName} • {qa.date}
          {qa.serviceType && (
            <Badge variant="default" className="shrink-0">
              {qa.serviceType}
            </Badge>
          )}
        </p>
      </div>
      <div className="pl-7 p-6 border-primary bg-background rounded-sm drop-shadow-sm">
        <p className="text-sm text-muted-foreground leading-6">{qa.answer}</p>
      </div>
    </div>
  ));
}

function QuestionsTab() {
  const {
    searchQuery,
    setSearchQuery,
    questions,
    questionsPage,
    setQuestionsPage,
    selectedQuestionServiceTypes,
    isLoadingQuestions,
  } = useOrderDialog();

  const itemsPerPage = 3;

  const getPageNumbers = (currentPage: number, totalPages: number) => {
    const pages: (number | "ellipsis")[] = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      pages.push(1);
      if (currentPage > 3) {
        pages.push("ellipsis");
      }
      const start = Math.max(2, currentPage - 1);
      const end = Math.min(totalPages - 1, currentPage + 1);
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      if (currentPage < totalPages - 2) {
        pages.push("ellipsis");
      }
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }
    return pages;
  };

  const filteredQuestions = questions.filter((question) => {
    if (
      searchQuery &&
      !question.question.toLowerCase().includes(searchQuery.toLowerCase()) &&
      !question.userName.toLowerCase().includes(searchQuery.toLowerCase()) &&
      !question.answer?.toLowerCase().includes(searchQuery.toLowerCase())
    ) {
      return false;
    }

    if (selectedQuestionServiceTypes.length > 0) {
      if (!question.serviceType) {
        return selectedQuestionServiceTypes.includes("Genel");
      }
      return selectedQuestionServiceTypes.includes(question.serviceType);
    }

    return true;
  });

  const paginatedQuestions = filteredQuestions.slice(
    (questionsPage - 1) * itemsPerPage,
    questionsPage * itemsPerPage,
  );

  return (
    <TabsContent
      value="soru-cevap"
      className="mt-0 h-full flex flex-col space-y-4 flex-1"
      data-tab="soru-cevap"
    >
      {/* Header with Statistics and Controls */}
      <div className="-mt-2 flex items-center justify-between">
        <p className="text-sm text-muted-foreground fake-text-stroke-muted">
          GeceYıldızı {questions.length} soruya cevap verdi.
        </p>

        {/* Controls: Ask Question, Filter, Search */}
        <div className="flex gap-2">
          <Button size="small">
            <Plus className="size-4 stroke-4" />
            Soru Sor
          </Button>

          <QuestionFilterDropdown />

          <Search
            placeholder="Sorularda ara..."
            value={searchQuery}
            onValueChange={setSearchQuery}
            className="w-60"
          />
        </div>
      </div>

      {/* Questions List */}
      <div className="flex-1 tracking-wide overflow-y-scroll space-y-8 scrollbar pb-15 pt-4 pr-2">
        <QuestionsList
          questions={paginatedQuestions}
          isLoading={isLoadingQuestions}
        />
      </div>

      {/* Pagination */}
      <Pagination>
        <PaginationContent className="flex justify-between gap-2 w-full">
          <PaginationItem>
            <PaginationPrevious
              href="#"
              onClick={(e) => {
                e.preventDefault();
                if (questionsPage > 1) setQuestionsPage(questionsPage - 1);
              }}
              className={
                questionsPage === 1 ? "pointer-events-none opacity-50" : ""
              }
            />
          </PaginationItem>

          <div className="flex gap-2">
            {getPageNumbers(
              questionsPage,
              Math.ceil(filteredQuestions.length / itemsPerPage),
            ).map((page, index) => (
              <PaginationItem key={index}>
                {page === "ellipsis" ? (
                  <PaginationEllipsis />
                ) : (
                  <PaginationLink
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      setQuestionsPage(page as number);
                    }}
                    isActive={questionsPage === page}
                    size="icon"
                  >
                    {page}
                  </PaginationLink>
                )}
              </PaginationItem>
            ))}
          </div>

          <PaginationItem>
            <PaginationNext
              href="#"
              onClick={(e) => {
                e.preventDefault();
                if (
                  questionsPage <
                  Math.ceil(filteredQuestions.length / itemsPerPage)
                ) {
                  setQuestionsPage(questionsPage + 1);
                }
              }}
              className={
                questionsPage ===
                Math.ceil(filteredQuestions.length / itemsPerPage)
                  ? "pointer-events-none opacity-50"
                  : ""
              }
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </TabsContent>
  );
}

function LeftColumn() {
  const {
    serviceImage,
    serviceTitle,
    serviceDescription,
    activityDetails,
    activityData,
    isLoadingActivityData,
  } = useOrderDialog();

  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);
  const charLimit = 140;

  // Get all activity details as an array
  const getAllActivityDetails = () => {
    if (!activityData?.details) {
      // Fallback to props data
      const details = [
        { label: "Rank", value: activityDetails.rank },
        { label: "Sunucu", value: activityDetails.server },
        { label: "Karakter", value: activityDetails.character },
        { label: "Kullanıcı Adı", value: activityDetails.nickname },
      ];

      // Add optional details if they exist
      if (activityDetails.platform) {
        details.push({ label: "Platform", value: activityDetails.platform });
      }
      if (activityDetails.style) {
        details.push({ label: "Stil", value: activityDetails.style });
      }
      if (activityDetails.personality) {
        details.push({ label: "Kişilik", value: activityDetails.personality });
      }

      return details;
    }

    // Use React Query data
    return Object.entries(activityData.details).map(([label, value]) => ({
      label,
      value,
    }));
  };

  const details = getAllActivityDetails();

  return (
    <div className="w-1/3 relative">
      <div className="pl-8 mt-4">
        <ActivityThumbnail
          src={activityData?.image || serviceImage}
          alt={serviceTitle}
          isLoading={isLoadingActivityData}
        />
        <ActivityDescription
          isLoadingActivityData={isLoadingActivityData}
          summary={activityData?.summary || serviceDescription}
          isExpanded={isDescriptionExpanded}
          onToggle={() => {
            setIsDescriptionExpanded(!isDescriptionExpanded);
          }}
          charLimit={charLimit}
        />
        {!isDescriptionExpanded && (
          <ActivityDetails
            isLoadingActivityData={isLoadingActivityData}
            details={details}
          />
        )}
      </div>
      {/* <ActivityStats /> */}
    </div>
  );
}

function ActivityThumbnail({
  src,
  alt,
  isLoading,
}: {
  src: string;
  alt: string;
  isLoading: boolean;
}) {
  if (isLoading) {
    return <Skeleton className="aspect-video rounded-lg shrink-0 mb-2" />;
  }

  return (
    <div className="aspect-video relative rounded-lg overflow-hidden shrink-0 mb-2">
      <Image src={src} alt={alt} className="object-cover" fill sizes="100" />
      <div className="absolute -inset-0 border-4 border-foreground rounded-lg"></div>
    </div>
  );
}

function ActivityStats() {
  return (
    <div className="flex justify-between w-full gap-2 text-shadow-none ml-8 pl-1 pr-8 mt-12">
      <ActivityBadge count={34}>
        <ConciergeBell className="fill-background stroke-0" />
      </ActivityBadge>
      <ActivityBadge count={210}>
        <UserStar className="fill-background stroke-0" />
      </ActivityBadge>
      <ActivityBadge count={12}>
        <Pencil className="fill-background stroke-0" />
      </ActivityBadge>
    </div>
  );
}

interface ActivityDetail {
  label: string;
  value: string;
}

function ActivityBadge({
  count,
  children,
}: {
  count: number;
  children: React.ReactNode;
}) {
  return (
    <div className="flex flex-1">
      <div className="text-sm bg-foreground text-background shrink-0 py-1 flex items-center w-10 justify-center h-7 [&_svg]:size-4 rounded-l-full">
        {/* {children} */}
      </div>
      <div className="text-xs w-full justify-center shrink px-2 py-1 flex items-center text-muted-foreground border-2 border-foreground border-l-0 rounded-r-full bg-ring">
        {count}
      </div>
    </div>
  );
}

function ActivityDescription({
  isLoadingActivityData,
  summary,
  isExpanded,
  onToggle,
  charLimit,
}: {
  isLoadingActivityData: boolean;
  summary: string;
  isExpanded: boolean;
  onToggle: () => void;
  charLimit: number;
}) {
  return (
    <div className="px-2 mt-6">
      <div
        className={cn(
          "text-left",
          isExpanded
            ? "flex-1 overflow-y-auto scrollbar"
            : "max-h-32 overflow-y-auto scrollbar",
        )}
      >
        {isLoadingActivityData ? (
          <div className="space-y-2">
            <Skeleton className="h-4.5 w-full" />
            <Skeleton className="h-4.5 w-full" />
            <Skeleton className="h-4.5 w-full" />
            <Skeleton className="h-4.5 w-3/4" />
          </div>
        ) : (
          <div className="text-sm text-muted-foreground leading-6">
            {summary.length > charLimit ? (
              <>
                <p>
                  {isExpanded
                    ? summary
                    : `${summary.substring(0, charLimit)}...`}
                  <button
                    onClick={onToggle}
                    className="text-foreground hover:underline text-sm flex-shrink-0 ml-2"
                  >
                    {isExpanded ? "Daha Az" : "Devamı"}
                  </button>
                </p>
              </>
            ) : (
              <p>{summary}</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

function ActivityDetails({
  isLoadingActivityData,
  details,
}: {
  isLoadingActivityData: boolean;
  details: ActivityDetail[];
}) {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 4;
  const totalPages = Math.ceil(details.length / itemsPerPage);

  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentDetails = details.slice(startIndex, endIndex);

  const handlePrevious = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  return (
    <>
      <SeparatorWithPagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPrevious={handlePrevious}
        onNext={handleNext}
        isLoading={isLoadingActivityData}
      />
      <div>
        {isLoadingActivityData ? (
          <div className="pl-2 grid grid-cols-2 gap-4">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={`skeleton-${index}`} className="space-y-2">
                <Skeleton className="h-6 w-16" />
                <Skeleton className="h-6 w-2/3" />
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-2">
            <div className="pl-2 grid grid-cols-2 gap-y-6">
              {currentDetails.map((detail, index) => (
                <div key={index} className="space-y-1.5">
                  <LabelSection size="small">{detail.label}</LabelSection>
                  <div className="text-sm text-foreground ml-2">
                    {detail.value}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </>
  );
}

function RightColumn() {
  return (
    <div className="flex-1 flex flex-col">
      <div className="flex-1 overflow-hidden mt-4 bg-muted rounded-lg p-8 flex flex-col">
        <OrderTab />
        <ReviewsTab />
        <QuestionsTab />
      </div>
    </div>
  );
}

export function OrderDialog({
  open = false,
  onOpenChange,
  serviceImage = "/dialogs/order/16995404846901826.webp",
  serviceTitle = "Valorant Boost Hizmeti",
  serviceDescription = "Profesyonel oyuncularımızla rank yükseltme hizmeti",
  overallRating = 4.5,
  activityDetails = {
    rank: "Diamond 2",
    server: "EU West",
    character: "Jett Main",
    username: "GeceYıldızı",
    nickname: "GeceYıldızı#1234",
    platform: "PC",
    style: "Support",
    personality: "Chill",
  },
  quantityLabel = "turnuva",
  durationPerUnit = 30,
  durationUnit = "dakika",
  onOrderSubmit,
}: OrderDialogProps) {
  // React Query hooks for data fetching
  const { data: serviceOptions = [], isLoading: isLoadingServiceOptions } =
    useServiceOptions();
  const { data: extraOptions = [], isLoading: isLoadingExtraOptions } =
    useExtraOptions();
  const { data: reviews = [], isLoading: isLoadingReviews } = useReviews();
  const { data: questions = [], isLoading: isLoadingQuestions } =
    useQuestions();
  const { data: activityData, isLoading: isLoadingActivityData } =
    useActivityData();
  const [activeTab, setActiveTab] = useState("siparis");
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedService, setSelectedService] = useState<string | null>(null);
  const [selectedExtras, setSelectedExtras] = useState<string[]>([]);
  const [quantity, setQuantity] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [reviewsPage, setReviewsPage] = useState(1);
  const [questionsPage, setQuestionsPage] = useState(1);

  // Review filters and sorting state
  const [selectedServiceTypes, setSelectedServiceTypes] = useState<string[]>(
    [],
  );
  const [selectedStarRatings, setSelectedStarRatings] = useState<number[]>([]);
  const [reviewSortBy, setReviewSortBy] = useState("date-desc");

  // Question filters state
  const [selectedQuestionServiceTypes, setSelectedQuestionServiceTypes] =
    useState<string[]>([]);

  // Scroll-to-top functionality for pagination
  const handleReviewsPageChange = (page: number) => {
    setReviewsPage(page);
    // Scroll to top of the reviews content
    const reviewsContent = document.querySelector(
      '[data-tab="degerlendirme"] .overflow-y-scroll',
    );
    if (reviewsContent) {
      reviewsContent.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const handleQuestionsPageChange = (page: number) => {
    setQuestionsPage(page);
    // Scroll to top of the questions content
    const questionsContent = document.querySelector(
      '[data-tab="soru-cevap"] .overflow-y-scroll',
    );
    if (questionsContent) {
      questionsContent.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const handleServiceSelect = (serviceId: string | null) => {
    setSelectedService(serviceId);
  };

  const handleExtraToggle = (extraId: string) => {
    setSelectedExtras((prev) =>
      prev.includes(extraId)
        ? prev.filter((id) => id !== extraId)
        : [...prev, extraId],
    );
  };

  // Calculate total cost of selected extras
  const calculateExtrasCost = () => {
    return selectedExtras.reduce((total, extraId) => {
      const extra = extraOptions.find((e) => e.id === extraId);
      return total + (extra?.price || 0);
    }, 0);
  };

  const calculateTotalPrice = () => {
    let total = 0;

    // Add service price
    if (selectedService) {
      const service = serviceOptions.find((s) => s.id === selectedService);
      if (service) total += service.price;
    }

    // Add extras price
    selectedExtras.forEach((extraId) => {
      const extra = extraOptions.find((e) => e.id === extraId);
      if (extra) total += extra.price;
    });

    // Multiply by quantity
    return total * quantity;
  };

  const handleOrderSubmitInternal = () => {
    const orderData = {
      service: selectedService,
      extras: selectedExtras,
      quantity,
      totalPrice: calculateTotalPrice(),
    };

    if (onOrderSubmit) {
      onOrderSubmit(orderData);
    } else {
      alert("Sipariş başarıyla gönderildi!");
    }
  };

  const contextValue: OrderDialogContext = {
    // Service data
    serviceImage: serviceImage!,
    serviceTitle: serviceTitle!,
    serviceDescription: serviceDescription!,
    overallRating: overallRating!,

    // Activity details
    activityDetails: activityDetails!,

    // Customization
    quantityLabel: quantityLabel!,
    durationPerUnit: durationPerUnit!,
    durationUnit: durationUnit!,

    // Order state
    activeTab,
    setActiveTab,
    currentStep,
    setCurrentStep,
    selectedService,
    setSelectedService,
    selectedExtras,
    setSelectedExtras,
    quantity,
    setQuantity,
    searchQuery,
    setSearchQuery,
    reviewsPage,
    setReviewsPage: handleReviewsPageChange,
    questionsPage,
    setQuestionsPage: handleQuestionsPageChange,

    // Review filters and sorting
    selectedServiceTypes,
    setSelectedServiceTypes,
    selectedStarRatings,
    setSelectedStarRatings,
    reviewSortBy,
    setReviewSortBy,

    // Question filters
    selectedQuestionServiceTypes,
    setSelectedQuestionServiceTypes,

    // Data and loading states
    serviceOptions,
    extraOptions,
    reviews,
    questions,
    activityData,
    isLoadingServiceOptions,
    isLoadingExtraOptions,
    isLoadingReviews,
    isLoadingQuestions,
    isLoadingActivityData,

    // Functions
    calculateTotalPrice,
    calculateExtrasCost,
    handleServiceSelect,
    handleExtraToggle,
    handleOrderSubmit: handleOrderSubmitInternal,
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[1200px] h-[85vh] max-h-[900px] overflow-hidden w-full max-w-full flex flex-col pl-0 pt-8 pb-8 pr-8"
        onEscapeKeyDown={(e) => e.preventDefault()}
        onPointerDownOutside={(e) => e.preventDefault()}
      >
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="flex flex-col h-full"
        >
          <DialogHeader className="flex-row gap-5">
            <DialogTitle variant="dotted">
              <Image
                src="/mocks/icon/valorant-icon.jpg"
                alt="Valorant"
                width={100}
                height={100}
                className="shrink-0 size-12 rounded-sm border-2 border-background ring-foreground ring-4"
              />
              <span className="text-lg text-background ">GeceYıldızı</span>
              <StarRating value={5} size="large" className="ml-auto pr-2" />
            </DialogTitle>
            <TabsList className="grid w-full max-w-xl grid-cols-3 ml-auto">
              <TabsTrigger value="siparis">{"SİPARİŞ"}</TabsTrigger>
              <TabsTrigger value="degerlendirme">{"DEĞERLENDİRME"}</TabsTrigger>
              <TabsTrigger value="soru-cevap">{"SORU-CEVAP"}</TabsTrigger>
            </TabsList>
          </DialogHeader>
          <OrderDialogContext.Provider value={contextValue}>
            <div className="flex flex-1 gap-10 overflow-hidden">
              <LeftColumn />
              <RightColumn />
            </div>
          </OrderDialogContext.Provider>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
