export type FolderManifest = Record<string, string[]>;

export const folderManifest: FolderManifest = {
  "/": [
    "alpha",
    "buttons",
    "dialogs",
    "hover",
    "inputs",
    "popovers",
    "stepper",
    "tabs",
    "test-dropdown"
  ],
  "/alpha": [
    "image",
    "svg",
    "svg-image"
  ],
  "/alpha/image": [],
  "/alpha/svg": [],
  "/alpha/svg-image": [],
  "/buttons": [
    "approval-onopen",
    "default",
    "default-and-primary",
    "disabled",
    "error-button",
    "group",
    "primary",
    "sizes",
    "toggle-group"
  ],
  "/buttons/approval-onopen": [],
  "/buttons/default": [],
  "/buttons/default-and-primary": [],
  "/buttons/disabled": [],
  "/buttons/error-button": [],
  "/buttons/group": [],
  "/buttons/primary": [],
  "/buttons/sizes": [],
  "/buttons/toggle-group": [],
  "/dialogs": [
    "activity",
    "bank-transfer",
    "iban",
    "info",
    "kyc",
    "nested",
    "order",
    "order-confirmation-page",
    "profile",
    "wallet",
    "withdrawal"
  ],
  "/dialogs/activity": [
    "normal"
  ],
  "/dialogs/activity/normal": [],
  "/dialogs/bank-transfer": [],
  "/dialogs/iban": [],
  "/dialogs/info": [],
  "/dialogs/kyc": [],
  "/dialogs/nested": [],
  "/dialogs/order": [
    "normal"
  ],
  "/dialogs/order/normal": [],
  "/dialogs/order-confirmation-page": [
    "no-service-modifier",
    "normal",
    "too-much-text"
  ],
  "/dialogs/order-confirmation-page/no-service-modifier": [],
  "/dialogs/order-confirmation-page/normal": [],
  "/dialogs/order-confirmation-page/too-much-text": [],
  "/dialogs/profile": [],
  "/dialogs/wallet": [],
  "/dialogs/withdrawal": [],
  "/hover": [
    "tooltips"
  ],
  "/hover/tooltips": [
    "notification"
  ],
  "/hover/tooltips/notification": [],
  "/inputs": [
    "checkbox",
    "nested-checkboxes",
    "validated-input"
  ],
  "/inputs/checkbox": [],
  "/inputs/nested-checkboxes": [],
  "/inputs/validated-input": [],
  "/popovers": [
    "approval"
  ],
  "/popovers/approval": [],
  "/stepper": [],
  "/tabs": [
    "four-tabs",
    "two-tabs"
  ],
  "/tabs/four-tabs": [],
  "/tabs/two-tabs": [],
  "/test-dropdown": []
} as const;
