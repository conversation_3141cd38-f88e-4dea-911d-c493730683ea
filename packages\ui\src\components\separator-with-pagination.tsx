"use client";

import { Separator } from "@workspace/ui/components/separator";
import { Play } from "lucide-react";
import FadeStaggerCircles from "@workspace/ui/components/icons/fade-stagger-circles";
import { cn } from "@workspace/ui/lib/utils";

interface SeparatorWithPaginationProps {
  currentPage: number;
  totalPages: number;
  onPrevious: () => void;
  onNext: () => void;
  isLoading?: boolean;
}

const PaginationButton = ({
  onClick,
  disabled,
  direction,
}: {
  onClick: () => void;
  disabled: boolean;
  direction: "previous" | "next";
}) => (
  <button
    onClick={onClick}
    disabled={disabled}
    className={cn(
      "disabled:opacity-50 flex items-center justify-center size-6 absolute bottom-1/2 translate-y-1/2",
      direction === "previous" ? "left-0.5" : "right-0.5",
    )}
  >
    <Play
      className={cn(
        "size-3.5 fill-background stroke-0",
        direction === "previous" && "rotate-180",
      )}
    />
  </button>
);

export function SeparatorWithPagination({
  currentPage,
  totalPages,
  onPrevious,
  onNext,
  isLoading = false,
}: SeparatorWithPaginationProps) {
  return (
    <div className="flex gap-1 items-center">
      <Separator className="shrink border-secondary data-[orientation=horizontal]:my-8" />
      <div className="relative w-25 bg-secondary rounded-md text-xs py-1 px-2 text-background text-shadow-none uppercase">
        <PaginationButton
          onClick={onPrevious}
          disabled={currentPage === 1 || isLoading}
          direction="previous"
        />
        <div className="w-full text-center h-4 mx-auto">
          {isLoading ? (
            <FadeStaggerCircles className={"mx-auto -translate-y-1.5 size-7"} />
          ) : (
            `${currentPage}/${totalPages}`
          )}
        </div>
        <PaginationButton
          onClick={onNext}
          disabled={currentPage === totalPages || isLoading}
          direction="next"
        />
      </div>
    </div>
  );
}
