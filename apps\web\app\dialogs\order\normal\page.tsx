"use client";

import { OrderDialog } from "@/components/dialogs/order.dialog";

export default function OrderDialogNormal() {
  const handleOrderSubmit = (orderData: any) => {
    console.log("Order submitted:", orderData);
    alert("Sipariş başarıyla gönderildi!");
  };

  return (
    <OrderDialog
      open={true}
      onOpenChange={() => {}}
      serviceImage="/dialogs/order/16995403695576185.webp"
      serviceTitle="Valorant"
      serviceDescription="Aslında oynamaya devam ettiğim ve geliştiğim ilk FPS oyunum!! İlk sezonda Iron 3 olarak başladım, şu an Plat 3'üm (✿◠‿◠) FADE takıntılı bir oyuncuyum!! Seninle oynamayı, öğretmeyi/senden öğrenmeyi ve oyunlarımızı kazanırken güzel bir sohbet etmeyi çok isterim 💕 EUW/NA sunucularında oynuyorum. Bu hizmet ile birlikte rank yükseltme, aim geliştirme ve takım oyunu stratejilerini öğrenebilirsiniz."
      overallRating={4.8}
      activityDetails={{
        rank: "Diamond 2",
        server: "EU West",
        character: "Jett Main",
        username: "GeceYıldızı",
        nickname: "nightstar#4567",
        platform: "PC",
        style: "Aggressive",
        personality: "Friendly",
      }}
      onOrderSubmit={handleOrderSubmit}
    />
  );
}
