"use client";

import { Check, Pencil } from "lucide-react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@workspace/ui/lib/utils";
import { Button } from "@workspace/ui/components/button";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

// Types for step configuration
export interface StepConfig {
  id: string;
  label: string;
  disabled?: boolean;
  completed?: boolean;
}

// Context for sharing stepper state
interface StepperContextValue {
  currentStep: number;
  totalSteps: number;
  nextStep: () => void;
  prevStep: () => void;
  goToStep: (stepIndex: number) => void;
  canGoNext: boolean;
  canGoPrev: boolean;
  isStepActive: (stepIndex: number) => boolean;
  isStepCompleted: (stepIndex: number) => boolean;
  steps: StepConfig[];
}

const StepperContext = createContext<StepperContextValue | null>(null);

interface StepContextValue {
  stepIndex: number;
  isActive: boolean;
  isCompleted: boolean;
}
const StepContext = createContext<StepContextValue | null>(null);

function useStepper() {
  const context = useContext(StepperContext);
  if (!context) {
    throw new Error("Stepper components must be used within a StepperVertical");
  }
  return context;
}

// Circle indicator variants
const stepCircleVariants = cva(
  "flex items-center justify-center rounded-full text-sm font-extrabold font-mono",
  {
    variants: {
      state: {
        active: "cursor-auto border-3 text-foreground border-foreground",
        inactive:
          "bg-muted-foreground/20 text-muted-foreground border-muted-foreground",
        completed: "bg-foreground text-background border-foreground",
        completed_unreached:
          "bg-muted-foreground/20 text-muted-foreground border-muted-foreground",
      },
      size: {
        default: "size-8",
        large: "size-10 text-base",
      },
    },
    defaultVariants: {
      state: "inactive",
      size: "default",
    },
  },
);

// Line connector variants
const stepLineVariants = cva("w-1 h-full", {
  variants: {
    state: {
      active: "bg-muted-foreground/12",
      inactive: "bg-muted-foreground/12",
      completed: "bg-foreground",
      completed_unreached: "bg-muted-foreground/12",
    },
  },
  defaultVariants: {
    state: "inactive",
  },
});

// Main Stepper Component
interface StepperVerticalProps {
  steps: StepConfig[];
  currentStep?: number;
  onStepChange?: (stepIndex: number) => void;
  className?: string;
  children?: React.ReactNode;
  allowSkipping?: boolean; // Allow jumping to any step
  autoScroll?: boolean; // Enable automatic scrolling to active step
}

function StepperVertical({
  steps,
  currentStep = 0,
  onStepChange,
  className,
  children,
  allowSkipping = true,
  autoScroll = true,
}: StepperVerticalProps) {
  const [internalStep, setInternalStep] = useState(currentStep);
  const stepperRef = useRef<HTMLDivElement>(null);

  // Use internal state if no external control is provided
  const activeStep = onStepChange ? currentStep : internalStep;

  // We're managing our own state instead of using headless-stepper

  const goToStep = useCallback(
    (stepIndex: number) => {
      if (
        stepIndex >= 0 &&
        stepIndex < steps.length &&
        !steps[stepIndex]?.disabled
      ) {
        if (
          allowSkipping ||
          stepIndex <= activeStep + 1 ||
          stepIndex < activeStep
        ) {
          if (onStepChange) {
            onStepChange(stepIndex);
          } else {
            setInternalStep(stepIndex);
          }
        }
      }
    },
    [steps, onStepChange, allowSkipping, activeStep],
  );

  // Auto-scroll effect when active step changes
  useEffect(() => {
    if (autoScroll && stepperRef.current) {
      // Find the active step element
      const activeStepElement = stepperRef.current.querySelector(
        `[data-step-index="${activeStep}"]`,
      );

      if (activeStepElement) {
        activeStepElement.scrollIntoView({
          behavior: "smooth",
          block: "nearest",
          inline: "nearest",
        });
      }
    }
  }, [activeStep, autoScroll]);

  const contextValue: StepperContextValue = useMemo(
    () => ({
      currentStep: activeStep,
      totalSteps: steps.length,
      nextStep: () => {
        const nextStepIndex = activeStep + 1;
        if (nextStepIndex < steps.length) {
          goToStep(nextStepIndex);
        }
      },
      prevStep: () => {
        const prevStepIndex = activeStep - 1;
        if (prevStepIndex >= 0) {
          goToStep(prevStepIndex);
        }
      },
      goToStep,
      canGoNext: activeStep < steps.length - 1,
      canGoPrev: activeStep > 0,
      isStepActive: (stepIndex: number) => stepIndex === activeStep,
      isStepCompleted: (stepIndex: number) =>
        stepIndex < activeStep || !!steps[stepIndex]?.completed,
      steps,
    }),
    [activeStep, steps, goToStep],
  );

  return (
    <StepperContext.Provider value={contextValue}>
      <div ref={stepperRef} className={cn("flex flex-col", className)}>
        {children}
      </div>
    </StepperContext.Provider>
  );
}

// Step Component - Individual step with navigation and content
interface StepProps {
  stepId: string;
  children?: React.ReactNode;
  className?: string;
  size?: VariantProps<typeof stepCircleVariants>["size"];
}

function Step({ stepId, children, className, size = "default" }: StepProps) {
  const { steps, isStepActive, isStepCompleted, goToStep, currentStep } =
    useStepper();

  const stepIndex = steps.findIndex((s) => s.id === stepId);
  const step = steps[stepIndex];
  if (!step) return null;

  if (stepIndex > currentStep && !step.completed) {
    return null;
  }

  const lastVisibleStepIndex = steps.reduce((lastIndex, step, index) => {
    const isVisible = index <= currentStep || !!step.completed;
    return isVisible ? index : lastIndex;
  }, -1);

  const isActive = isStepActive(stepIndex);
  const isCompleted = isStepCompleted(stepIndex);

  let state: "active" | "completed" | "completed_unreached" | "inactive" =
    "inactive";
  if (isActive) {
    state = "active";
  } else if (isCompleted) {
    if (stepIndex < currentStep) {
      state = "completed";
    } else {
      state = "completed_unreached";
    }
  }

  const stepContextValue = { stepIndex, isActive, isCompleted };

  return (
    <StepContext.Provider value={stepContextValue}>
      <div
        className={cn("flex items-stretch", className)}
        data-step-index={stepIndex}
      >
        {/* Navigation Circle and Line */}
        <div className="flex flex-col items-center translate-y-1.5">
          <button
            onClick={() => goToStep(stepIndex)}
            disabled={step.disabled}
            className={cn(
              stepCircleVariants({ state, size }),
              "disabled:cursor-not-allowed shrink-0 text-center text-base",
            )}
            aria-label={`Adım ${stepIndex + 1}`}
          >
            {(() => {
              const iconSize = size === "large" ? 20 : 16;
              if (isCompleted && !isActive) {
                return <Check size={iconSize} strokeWidth={4} />;
              }
              return stepIndex + 1;
            })()}
          </button>
          {stepIndex !== lastVisibleStepIndex && (
            <div
              className={cn(
                stepLineVariants({
                  state: state,
                }),
              )}
            />
          )}
        </div>

        {/* Content Area */}
        <div className="flex-1 ml-4 pb-4">{children}</div>
      </div>
    </StepContext.Provider>
  );
}

// Previous Button
interface StepPreviousButtonProps {
  className?: string;
  children?: React.ReactNode;
}

function StepPreviousButton({
  className,
  children = "Önceki",
}: StepPreviousButtonProps) {
  const { prevStep, canGoPrev } = useStepper();

  return (
    <Button
      onClick={prevStep}
      disabled={!canGoPrev}
      variant="secondary"
      size="small"
      className={className}
    >
      {children}
    </Button>
  );
}

// Next Button
interface StepNextButtonProps {
  className?: string;
  children?: React.ReactNode;
}

function StepNextButton({
  className,
  children = "Sonraki",
}: StepNextButtonProps) {
  const { nextStep, canGoNext } = useStepper();

  return (
    <Button
      onClick={nextStep}
      disabled={!canGoNext}
      variant="primary"
      size="small"
      className={className}
    >
      {children}
    </Button>
  );
}

// Step Activation Button
interface StepActivationButtonProps {
  stepId: string;
  children: React.ReactNode;
  className?: string;
}

function StepActivationButton({
  stepId,
  children,
  className,
}: StepActivationButtonProps) {
  const { steps, goToStep } = useStepper();

  const stepIndex = steps.findIndex((step) => step.id === stepId);
  const step = steps[stepIndex];

  if (stepIndex === -1) {
    console.warn(`Step with id '${stepId}' not found`);
    return null;
  }

  return (
    <Button
      onClick={() => goToStep(stepIndex)}
      disabled={step?.disabled}
      className={className}
      size="small"
    >
      {children}
    </Button>
  );
}

// Step Content Components
interface StepContentProps {
  children: React.ReactNode;
  className?: string;
}

function StepContentActive({ children, className }: StepContentProps) {
  const stepContext = useContext(StepContext);

  const isActive = stepContext?.isActive ?? false;

  if (!isActive) {
    return null;
  }

  return <div className={cn("mb-10 mt-2", className)}>{children}</div>;
}

function StepContentInactive({ children, className }: StepContentProps) {
  const stepContext = useContext(StepContext);

  const isActive = stepContext?.isActive ?? false;
  const isCompleted = stepContext?.isCompleted ?? false;

  if (isActive || !isCompleted) {
    return null;
  }

  return <div className={cn("mb-2", className)}>{children}</div>;
}

export {
  StepperVertical,
  Step,
  StepPreviousButton,
  StepNextButton,
  StepActivationButton,
  StepContentActive,
  StepContentInactive,
};
